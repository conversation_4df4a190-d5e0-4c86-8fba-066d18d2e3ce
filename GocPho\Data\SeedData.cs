using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace GocPho.Data;

public static class SeedData
{
    public static async Task Initialize(IServiceProvider serviceProvider, 
        UserManager<IdentityUser> userManager, 
        RoleManager<IdentityRole> roleManager)
    {
        var context = serviceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Ensure database is created
        await context.Database.EnsureCreatedAsync();
        
        // Create roles
        await CreateRoles(roleManager);
        
        // Create admin user
        await CreateAdminUser(userManager);
    }
    
    private static async Task CreateRoles(RoleManager<IdentityRole> roleManager)
    {
        string[] roleNames = { "Admin", "Customer" };
        
        foreach (var roleName in roleNames)
        {
            var roleExist = await roleManager.RoleExistsAsync(roleName);
            if (!roleExist)
            {
                await roleManager.CreateAsync(new IdentityRole(roleName));
            }
        }
    }
    
    private static async Task CreateAdminUser(UserManager<IdentityUser> userManager)
    {
        var adminEmail = "<EMAIL>";
        var adminUser = await userManager.FindByEmailAsync(adminEmail);
        
        if (adminUser == null)
        {
            var newAdminUser = new IdentityUser
            {
                UserName = "admin",
                Email = adminEmail,
                EmailConfirmed = true
            };
            
            var result = await userManager.CreateAsync(newAdminUser, "admin@123");
            
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(newAdminUser, "Admin");
            }
        }
    }
}
