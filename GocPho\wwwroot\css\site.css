html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Roboto', sans-serif;
  line-height: 1.6;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

/* Custom styles for Góc phố Coffee */
.hero-section {
  min-height: 70vh;
}

.min-vh-50 {
  min-height: 50vh;
}

.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.feature-icon {
  transition: transform 0.3s ease;
}

.feature-icon:hover {
  transform: scale(1.1);
}

.navbar-brand {
  font-size: 1.5rem;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
  color: #000;
}

.text-warning {
  color: #ffc107 !important;
}

/* Cart badge */
#cart-count {
  font-size: 0.75rem;
  min-width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Admin styles */
.admin-card {
  border-left: 4px solid #ffc107;
}

.admin-sidebar {
  background-color: #f8f9fa;
  min-height: calc(100vh - 76px);
}

/* Form styles */
.form-control:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Table styles */
.table-hover tbody tr:hover {
  background-color: rgba(255, 193, 7, 0.1);
}

/* Status badges */
.status-pending { background-color: #ffc107; }
.status-confirmed { background-color: #17a2b8; }
.status-preparing { background-color: #fd7e14; }
.status-delivering { background-color: #6f42c1; }
.status-delivered { background-color: #28a745; }
.status-cancelled { background-color: #dc3545; }

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section .lead {
    font-size: 1rem;
  }

  .display-5 {
    font-size: 2rem;
  }
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}