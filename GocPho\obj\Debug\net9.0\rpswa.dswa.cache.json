{"GlobalPropertiesHash": "JkkzkOhgkfN7YewvJS+SvTSN9W4USliRMuLNkuqGgGI=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["WEA6ymxuS9QpAI7TnZ4l34uMf0z282rXXfrdtS7G088=", "5n4MGf7NFjJOY23NELBAe+bML7bh9inh8t6LXHEA/AE=", "UMVc2QltnAs0EBQMStBBzWaqCdnsR3bR0kmumfVNyag=", "tUhO342pzjZm8mhtBOlaVOzCPWr+QXri+Zv1nSAw3rY=", "WBLBq6aPN8PPCo3jjGNanxGdDviO1RT7ImrAqlve8So=", "A+VzgPKogOVbvsiaXNCcZRRF+hEg3eNhqF+svQAwS7E=", "qlJi5JThHqCj+FbYDvL2nf+VbLvhqEdHMOKkRIpIm3Y=", "kcqaiW5QWpfu0XfvM3ppCIVIPQVnIZDs5MRoJRfwbKA=", "4gJAvvd6ni6qZfeNg1NVIsahdUEFqayH/THi4hph2T8=", "gDj4kn8yOHi+p2IUcxMwMbdRw1/QxGbJlYaDjZFAiyw=", "t0jTPFA8D60mWyGscpDt13CMxhjJ51tiXnxhh9kT5Os=", "BF6+FG6SkepyALo4qV3xH5575NtayzlEKWtBQCXhK9E=", "e7GuA0CRP1NNUIuv890TL8H4uarH/sp7Mbk1FrAxhUw=", "HOhZbmg+NdIjf+UynoREoqi6CJSScYRbdQdIHZcKAlk=", "V3Y2icIR83QtMZ2eKsEo3jKUuEUbsDKfqhokRDEO1cc=", "gxyrndvvh2lP2yAJOAv5bDuP/0q+D7KYVb4CIQH0iI4=", "0G8YGGhdBGE5g3E635joIT/V2YzJsxXM+QS4XG0o1QM=", "roE+2aME2dZFiKpyvltGu7obT0+dJSPO3yy75ayNdOg=", "0tyg6d5tbkhZo2cc7CKeWetHs2sHVIeSEn+nIWJpuLY=", "8SMweRDGq4NIpE5k+lvLAxnJGQzvrpHpZsAfdD357t8=", "RISa1Uc4pjvS06dBgcxp3xoiHGfuugeLgYRhbdWQojs=", "BggJ6xSsvi3Z6Y5UNApiB1k22pzAUA4pg43Fi/IEpTI=", "+Ps8NWgIlBl9JOa9m/43ftQG6ckMGcezbwqXkdavnKA=", "poDK9wb2RanYgZukSABsHP2+sVw4NSIVbUD8ickqw08=", "MzNibJ9QdTzEri3pK5j0gaYm3tB7jOTUxcRa6ifvG+s=", "JdUrZhhciq4euyVQVXFYM6n0Oi7j7fTQ5Xfg/9MHoko=", "5Bfm2kCXLPZZ+cGJyrcfYiNXjV5hwDtjBUhkzRM/7So=", "rWI6MfbC4pYb9xXun6/KIdYOtwgWZfFTcvg4cQMN0WE=", "DMV+VxG0iv4lve/IC4EwguxwYlvfp71QPr6ZvQTfAFA=", "pqh+rFWkfPf4dl+3qEy+vJP4/yjiYGkUN48FpbA1sWY=", "MAOWJNVfM/1YPlOOJC7wCcpNAD0k2RWnTuvNkRH/UjA=", "VjG0YmJKSnIUZQmapOIc10+9YrKFXRSP3GKpob/oqNw=", "SPedFml7J4igrkN57grzg/nwrY1FnGZNIVgRE/InjaI=", "GYPuFPROSTNDEhbPYOj8c4m4QnpuXp/X0aKMmKaT8/g=", "G+I8JMopEMu+VQ+ri/ObScEjJSR+VSqYT79DnV9Q9vs=", "iP6VxaEPgekr0iKPK2SjVnARtqJbRjVrzqFf+bhUqxw=", "Hbc/sGJVqI7yh0SLKUcUERA2gO42XJukdzKYuncwVVY=", "D9p0ZRuCMPPrXscYNsQKgwntOPyv8aVld0vNFn7xq50=", "n9i4abV+5PhtP/fspsKM6HgWUPVJOn0k/nxowgfUFYo=", "5osw/rL2nrpVCgizORs3Cpy7OXYtgqFagw1692Eb/sY=", "beTYsQO3Y5S0Pa7tX1zQn4/Pr8qTUriA8RKF44H6Hm0=", "f26AYBqQWK7KY5AROBjzxdzWSuEaq4TFkiKFN3jLfOU=", "eM5HclLM7CTBdNTrLsScNmnHkjnfKs+veCQEt4oM/pk=", "pqW8cuI0ynoTknpVmQsXRSdUeSWp8XWg0aIVJgquT0M=", "Si3Uf0FvQyOHhsSImto1KEEVOP4HIj8SldcB9Wy1YpI=", "gj/5tzXiOG3I+TymUNS5uIVE5MVP2CoG7iXzV5di0Ew=", "U+Tln+jH/54qoDdkRVQ3B17PUXf88l93cfV0vvlbSFw=", "CTUQlk/im4QU39kOCEQAUIh/yR19dtFlqZXuopCkLwM=", "SUxsbp3qoho7jRgU35dRMXg/obV/Um06e7aA4raiEaA=", "EhgGbYDo0lXt8Rw9cbR+1iHL4TrqdfZ7yJ/RMD/EHuw=", "zTZjsulr2rZVnj+/ANsC+8/+bTPdYqxN1UQKS+xWbBk=", "PoEFQrihfJNq5ZvWqRkfgcgvXFA3Vq9yJ6WuXonxspU=", "cou8QOOZsvan3NiUfPt+slL6/gnHM5tsOX9QjFH0Em0=", "Zs1xp38PKSVrN91i2CEcQkk2JxmOu0VrtyO/CUkrc88=", "HLSRngKk/kaLXoVyt/JUKsXsT3FNlzNbHpGufllTcM4=", "PnAg8FaxKizQ7SdAi5J78+9I4No5HZs77DYI/A5Pbts=", "DOkA6UCA4Gaw8x0zimVWT+5r1pCQJyI1PYm42cRMHd4=", "PzpxCDI+Kv34fFtlPSWWGyOgdv5ipENDrWybTYaEUU4=", "+VYth8Yu+baeYvqdGME7OTrxFRvHatLCWBGuOXmv+n8=", "2iPUieTZJPbjrL9NTCMz9i5fVV/0JkUw3Ef5Yxr9kxs=", "/QvHUNDr5YI6OHiYFdsCv45kw3GdYMDVdHdvIoOQPUg=", "k83fcpTJuKJfVxPP/cZpYPDo5lVSQepT3AgmxSH4ym8=", "fv+0V8bJssJHUDpSFbLQQckjFhPQ+WmGA4Kh0QkD8cw=", "biABditfOEaZMBqkC7sJj4xanpKQFV7+og9zDByvuAU=", "uwTe0FtraZo69VE1x0fQnVkeR4hyktl6RFJmzb38XQk=", "TvXIakPkIFts9/ggHUcR+fupJyjyQfEGvKrEA4BLdr0=", "g1vt89FFDcjE2HVDL+km2fwWChK1Ka/7W2q4gGnUG/c=", "fh7qriLcz7Z055/naLn0qBOwSqDLsYTbBQ2GcUJJR5w=", "EgX7xx9EWTBbjx12fqdU299iYkeasHEMik4EMprJO6g=", "IFPNaTy9yKBmRyJUGDADSLvEdSY33TEhHfhXtvKmLbU=", "JjDim72jo8/wWlI/rzbHwAUNJz+kN7v/q+XrZXZq4fg=", "fPp2bZT8/g4cu2HX7gzbDILj4Y1Ax+c4lG5RiyZeTjM=", "hYm6gAs/bP+Xw3y93mfaEf55aVWGZPX+v3rL+dxi8sM=", "Hx1FuwEXBSkLoKobq0MqjG2xkkNf0V5m4iFRd8u2aJ8=", "fUk4wxrIzOcFdToA8sYNJD2yoel41LUMlKI+klcohTs=", "OLztGaVWKiA8TlowiVD16JKArqAtePCruLTEaIeBTEI=", "0HncQ26yYEEDBEiPEHQQv1Joe8mb8+uteAEl42Pe0Js=", "WJokEiU6h8+UeqeyD9EksubJOuorqQ/hm+hNx5kM0ZU=", "T7a7KMY2Wenm7Jl0R6NMRSV1i0VRJnnQAifq+OQ7FgM=", "IS9eNn2LmhlFr6hFcvj5yFbanDjtLtgibyo9Ap3rZBg=", "4jXIAlSG0UqcsGSOqmaVm9WLyKg2MRIx2DS2mtBYStU=", "9qURjyWTLu+vCwOjEDi6l6CrHYSO7mtYf5W9DNn/diI=", "m30C+hbUM0GBxpT3mHXluesWJY74BOPWOULhJUv7eoo=", "yianjzayPd0solLCJ6CSMk5TGA89BKZOafUuZeBCdUE=", "JrKFTmi2oT5TumWj8+2yZ2LofzYWbwjVu9xpAZBiL8U="], "CachedAssets": {"WEA6ymxuS9QpAI7TnZ4l34uMf0z282rXXfrdtS7G088=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\css\\site.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7r03190iqp", "Integrity": "bfRFw7JJ8waVIdln06fi+gaoucufcQnWHOtI2hbPPJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 2519, "LastWriteTime": "2025-07-22T15:59:38.7958563+00:00"}, "5n4MGf7NFjJOY23NELBAe+bML7bh9inh8t6LXHEA/AE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\favicon.ico", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-22T15:50:49.3882062+00:00"}, "qlJi5JThHqCj+FbYDvL2nf+VbLvhqEdHMOKkRIpIm3Y=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\js\\site.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-22T15:50:49.4247556+00:00"}, "kcqaiW5QWpfu0XfvM3ppCIVIPQVnIZDs5MRoJRfwbKA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "4gJAvvd6ni6qZfeNg1NVIsahdUEFqayH/THi4hph2T8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "gDj4kn8yOHi+p2IUcxMwMbdRw1/QxGbJlYaDjZFAiyw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "t0jTPFA8D60mWyGscpDt13CMxhjJ51tiXnxhh9kT5Os=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "BF6+FG6SkepyALo4qV3xH5575NtayzlEKWtBQCXhK9E=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "e7GuA0CRP1NNUIuv890TL8H4uarH/sp7Mbk1FrAxhUw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "HOhZbmg+NdIjf+UynoREoqi6CJSScYRbdQdIHZcKAlk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-22T15:50:49.2773498+00:00"}, "V3Y2icIR83QtMZ2eKsEo3jKUuEUbsDKfqhokRDEO1cc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-22T15:50:49.2780405+00:00"}, "gxyrndvvh2lP2yAJOAv5bDuP/0q+D7KYVb4CIQH0iI4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "0G8YGGhdBGE5g3E635joIT/V2YzJsxXM+QS4XG0o1QM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "roE+2aME2dZFiKpyvltGu7obT0+dJSPO3yy75ayNdOg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "0tyg6d5tbkhZo2cc7CKeWetHs2sHVIeSEn+nIWJpuLY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "8SMweRDGq4NIpE5k+lvLAxnJGQzvrpHpZsAfdD357t8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "RISa1Uc4pjvS06dBgcxp3xoiHGfuugeLgYRhbdWQojs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "BggJ6xSsvi3Z6Y5UNApiB1k22pzAUA4pg43Fi/IEpTI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "+Ps8NWgIlBl9JOa9m/43ftQG6ckMGcezbwqXkdavnKA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "poDK9wb2RanYgZukSABsHP2+sVw4NSIVbUD8ickqw08=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "MzNibJ9QdTzEri3pK5j0gaYm3tB7jOTUxcRa6ifvG+s=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "JdUrZhhciq4euyVQVXFYM6n0Oi7j7fTQ5Xfg/9MHoko=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-22T15:50:49.2792307+00:00"}, "5Bfm2kCXLPZZ+cGJyrcfYiNXjV5hwDtjBUhkzRM/7So=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-22T15:50:49.2906632+00:00"}, "rWI6MfbC4pYb9xXun6/KIdYOtwgWZfFTcvg4cQMN0WE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-22T15:50:49.2913568+00:00"}, "DMV+VxG0iv4lve/IC4EwguxwYlvfp71QPr6ZvQTfAFA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-22T15:50:49.2928909+00:00"}, "pqh+rFWkfPf4dl+3qEy+vJP4/yjiYGkUN48FpbA1sWY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-22T15:50:49.2928909+00:00"}, "MAOWJNVfM/1YPlOOJC7wCcpNAD0k2RWnTuvNkRH/UjA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-22T15:50:49.2928909+00:00"}, "VjG0YmJKSnIUZQmapOIc10+9YrKFXRSP3GKpob/oqNw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-22T15:50:49.3040501+00:00"}, "SPedFml7J4igrkN57grzg/nwrY1FnGZNIVgRE/InjaI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-22T15:50:49.3060427+00:00"}, "GYPuFPROSTNDEhbPYOj8c4m4QnpuXp/X0aKMmKaT8/g=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-22T15:50:49.3060427+00:00"}, "G+I8JMopEMu+VQ+ri/ObScEjJSR+VSqYT79DnV9Q9vs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-22T15:50:49.3060427+00:00"}, "iP6VxaEPgekr0iKPK2SjVnARtqJbRjVrzqFf+bhUqxw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-22T15:50:49.3096504+00:00"}, "Hbc/sGJVqI7yh0SLKUcUERA2gO42XJukdzKYuncwVVY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-22T15:50:49.3096504+00:00"}, "D9p0ZRuCMPPrXscYNsQKgwntOPyv8aVld0vNFn7xq50=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-22T15:50:49.3143336+00:00"}, "n9i4abV+5PhtP/fspsKM6HgWUPVJOn0k/nxowgfUFYo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "5osw/rL2nrpVCgizORs3Cpy7OXYtgqFagw1692Eb/sY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "beTYsQO3Y5S0Pa7tX1zQn4/Pr8qTUriA8RKF44H6Hm0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "f26AYBqQWK7KY5AROBjzxdzWSuEaq4TFkiKFN3jLfOU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "eM5HclLM7CTBdNTrLsScNmnHkjnfKs+veCQEt4oM/pk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "pqW8cuI0ynoTknpVmQsXRSdUeSWp8XWg0aIVJgquT0M=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "Si3Uf0FvQyOHhsSImto1KEEVOP4HIj8SldcB9Wy1YpI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "gj/5tzXiOG3I+TymUNS5uIVE5MVP2CoG7iXzV5di0Ew=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "U+Tln+jH/54qoDdkRVQ3B17PUXf88l93cfV0vvlbSFw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "CTUQlk/im4QU39kOCEQAUIh/yR19dtFlqZXuopCkLwM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "SUxsbp3qoho7jRgU35dRMXg/obV/Um06e7aA4raiEaA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-22T15:50:49.3180065+00:00"}, "EhgGbYDo0lXt8Rw9cbR+1iHL4TrqdfZ7yJ/RMD/EHuw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-22T15:50:49.3306548+00:00"}, "zTZjsulr2rZVnj+/ANsC+8/+bTPdYqxN1UQKS+xWbBk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-22T15:50:49.3311842+00:00"}, "PoEFQrihfJNq5ZvWqRkfgcgvXFA3Vq9yJ6WuXonxspU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "cou8QOOZsvan3NiUfPt+slL6/gnHM5tsOX9QjFH0Em0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-22T15:50:49.3978927+00:00"}, "Zs1xp38PKSVrN91i2CEcQkk2JxmOu0VrtyO/CUkrc88=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-22T15:50:49.3978927+00:00"}, "HLSRngKk/kaLXoVyt/JUKsXsT3FNlzNbHpGufllTcM4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-22T15:50:49.2780405+00:00"}, "PnAg8FaxKizQ7SdAi5J78+9I4No5HZs77DYI/A5Pbts=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-22T15:50:49.3720205+00:00"}, "DOkA6UCA4Gaw8x0zimVWT+5r1pCQJyI1PYm42cRMHd4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-22T15:50:49.3874304+00:00"}, "PzpxCDI+Kv34fFtlPSWWGyOgdv5ipENDrWybTYaEUU4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-22T15:50:49.3882062+00:00"}, "+VYth8Yu+baeYvqdGME7OTrxFRvHatLCWBGuOXmv+n8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-22T15:50:49.3882062+00:00"}, "2iPUieTZJPbjrL9NTCMz9i5fVV/0JkUw3Ef5Yxr9kxs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-22T15:50:49.2773498+00:00"}, "/QvHUNDr5YI6OHiYFdsCv45kw3GdYMDVdHdvIoOQPUg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-22T15:50:49.3322521+00:00"}, "k83fcpTJuKJfVxPP/cZpYPDo5lVSQepT3AgmxSH4ym8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-22T15:50:49.3322521+00:00"}, "fv+0V8bJssJHUDpSFbLQQckjFhPQ+WmGA4Kh0QkD8cw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-22T15:50:49.3322521+00:00"}, "biABditfOEaZMBqkC7sJj4xanpKQFV7+og9zDByvuAU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-22T15:50:49.344565+00:00"}, "uwTe0FtraZo69VE1x0fQnVkeR4hyktl6RFJmzb38XQk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-22T15:50:49.344565+00:00"}, "TvXIakPkIFts9/ggHUcR+fupJyjyQfEGvKrEA4BLdr0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-22T15:50:49.3699297+00:00"}, "g1vt89FFDcjE2HVDL+km2fwWChK1Ka/7W2q4gGnUG/c=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-22T15:50:49.2718697+00:00"}, "UMVc2QltnAs0EBQMStBBzWaqCdnsR3bR0kmumfVNyag=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-cup.svg", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-cup#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "76dwl5a5pz", "Integrity": "r1OpHLNfufBHPpfpQoZMhO8yvl0NklycoEu2qbaJW3s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\coffee-cup.svg", "FileLength": 732, "LastWriteTime": "2025-07-22T16:04:34.6045757+00:00"}, "tUhO342pzjZm8mhtBOlaVOzCPWr+QXri+Zv1nSAw3rY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-hero.svg", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-hero#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "40h8kgmqbi", "Integrity": "FLxRyveRIi0XYW/KTVmY44DixlYrgrke36rmXRCOtwo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\coffee-hero.svg", "FileLength": 612, "LastWriteTime": "2025-07-22T16:04:24.75399+00:00"}, "WBLBq6aPN8PPCo3jjGNanxGdDviO1RT7ImrAqlve8So=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\default-coffee.svg", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "images/default-coffee#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ybrhg4el0e", "Integrity": "YpuIKG1JT3PXtakRhqlFqn2Nb9On/B31OU9FCmgWuH4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\default-coffee.svg", "FileLength": 602, "LastWriteTime": "2025-07-22T16:04:15.9742213+00:00"}, "A+VzgPKogOVbvsiaXNCcZRRF+hEg3eNhqF+svQAwS7E=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\placeholder-generator.html", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\", "BasePath": "_content/GocPho", "RelativePath": "images/placeholder-generator#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1n5x6omr1j", "Integrity": "zeC5SRq2k5MAvX5zMRLzhgIAeSXBJNLd3BFh/GHBHBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\placeholder-generator.html", "FileLength": 2374, "LastWriteTime": "2025-07-22T16:02:48.9015463+00:00"}}, "CachedCopyCandidates": {}}