@model GocPho.Models.AdminDashboardViewModel
@{
    ViewData["Title"] = "Quản trị - Dashboard";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-tachometer-alt text-warning me-2"></i>Dashboard Quản trị
                </h1>
                <div>
                    <span class="text-muted">Chào mừng, @User.Identity?.Name</span>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-xl-3 col-md-6">
                    <div class="card admin-card shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="text-muted mb-2">Tổng sản phẩm</h6>
                                    <h3 class="fw-bold">@Model.TotalProducts</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coffee fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card admin-card shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="text-muted mb-2">Tổng đơn hàng</h6>
                                    <h3 class="fw-bold">@Model.TotalOrders</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-cart fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card admin-card shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="text-muted mb-2">Đơn hàng chờ</h6>
                                    <h3 class="fw-bold text-warning">@Model.PendingOrders</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card admin-card shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="text-muted mb-2">Doanh thu</h6>
                                    <h3 class="fw-bold text-success">@Model.TotalRevenue.ToString("N0") VNĐ</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4 mb-5">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Thao tác nhanh
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <a href="@Url.Action("CreateProduct", "Admin")" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-plus-circle me-2"></i>Thêm sản phẩm
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="@Url.Action("Products", "Admin")" class="btn btn-outline-info w-100">
                                        <i class="fas fa-list me-2"></i>Quản lý sản phẩm
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="@Url.Action("Orders", "Admin")" class="btn btn-outline-success w-100">
                                        <i class="fas fa-shopping-bag me-2"></i>Quản lý đơn hàng
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="@Url.Action("Orders", "Admin", new { status = GocPho.Models.OrderStatus.Pending })" 
                                       class="btn btn-outline-danger w-100">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Đơn hàng chờ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Đơn hàng gần đây
                                </h5>
                                <a href="@Url.Action("Orders", "Admin")" class="btn btn-sm btn-outline-warning">
                                    Xem tất cả
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            @if (Model.RecentOrders.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Mã đơn</th>
                                                <th>Khách hàng</th>
                                                <th>Ngày đặt</th>
                                                <th>Tổng tiền</th>
                                                <th>Trạng thái</th>
                                                <th class="text-center">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var order in Model.RecentOrders)
                                            {
                                                <tr>
                                                    <td class="fw-bold">#@order.Id</td>
                                                    <td>@order.CustomerName</td>
                                                    <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                                    <td class="fw-bold">@order.TotalAmount.ToString("N0") VNĐ</td>
                                                    <td>
                                                        <span class="badge <EMAIL>().ToLower()">
                                                            @switch (order.Status)
                                                            {
                                                                case GocPho.Models.OrderStatus.Pending:
                                                                    <text>Chờ xác nhận</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Confirmed:
                                                                    <text>Đã xác nhận</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Preparing:
                                                                    <text>Đang chuẩn bị</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Delivering:
                                                                    <text>Đang giao hàng</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Delivered:
                                                                    <text>Đã giao hàng</text>
                                                                    break;
                                                                case GocPho.Models.OrderStatus.Cancelled:
                                                                    <text>Đã hủy</text>
                                                                    break;
                                                            }
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <a href="@Url.Action("OrderDetails", "Admin", new { id = order.Id })" 
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">Chưa có đơn hàng nào</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
