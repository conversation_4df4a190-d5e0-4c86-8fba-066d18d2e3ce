@model List<GocPho.Models.Product>
@{
    ViewData["Title"] = "Menu cà phê";
    var categories = ViewBag.Categories as List<GocPho.Models.Category>;
    var currentCategory = ViewBag.CurrentCategory as int?;
}

<div class="container py-5">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">Menu Góc phố Coffee</h1>
        <p class="lead text-muted">Khám phá những ly cà phê tuyệt vời của chúng tôi</p>
    </div>

    <!-- Category Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex flex-wrap justify-content-center gap-2">
                <a href="@Url.Action("Menu", "Product")" 
                   class="btn @(currentCategory == null ? "btn-warning" : "btn-outline-warning")">
                    <i class="fas fa-th-large me-1"></i><PERSON><PERSON><PERSON> c<PERSON>
                </a>
                @if (categories != null)
                {
                    @foreach (var category in categories)
                    {
                        <a href="@Url.Action("Menu", "Product", new { categoryId = category.Id })" 
                           class="btn @(currentCategory == category.Id ? "btn-warning" : "btn-outline-warning")">
                            <i class="fas fa-coffee me-1"></i>@category.Name
                        </a>
                    }
                }
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    @if (Model.Any())
    {
        <div class="row g-4">
            @foreach (var product in Model)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm border-0 product-card">
                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name"
                             style="height: 250px; object-fit: cover;"
                             onerror="this.src='/images/default-coffee.svg'">
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title mb-0">@product.Name</h5>
                                <span class="badge bg-warning text-dark">@product.Category?.Name</span>
                            </div>
                            <p class="card-text text-muted flex-grow-1">@product.Description</p>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <span class="h5 text-warning mb-0 fw-bold">@product.Price.ToString("N0") VNĐ</span>
                                @if (product.IsAvailable)
                                {
                                    @if (User.Identity?.IsAuthenticated == true)
                                    {
                                        <div class="btn-group">
                                            <button class="btn btn-warning btn-add-to-cart" data-product-id="@product.Id">
                                                <i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ
                                            </button>
                                            <a href="@Url.Action("Details", "Product", new { id = product.Id })" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="btn-group">
                                            <a href="@Url.Action("Login", "Account")" class="btn btn-outline-warning">
                                                Đăng nhập để mua
                                            </a>
                                            <a href="@Url.Action("Details", "Product", new { id = product.Id })" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Hết hàng</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-coffee fa-4x text-muted mb-3"></i>
            <h3 class="text-muted">Không có sản phẩm nào</h3>
            <p class="text-muted">Hiện tại chưa có sản phẩm trong danh mục này.</p>
            <a href="@Url.Action("Menu", "Product")" class="btn btn-warning">
                <i class="fas fa-arrow-left me-2"></i>Xem tất cả sản phẩm
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('.btn-add-to-cart').click(function() {
                var productId = $(this).data('product-id');
                var button = $(this);
                
                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin me-1"></i>Đang thêm...');
                
                $.post('/Cart/AddToCart', { productId: productId, quantity: 1 })
                    .done(function(response) {
                        if (response.success) {
                            showToast('success', response.message);
                            if (window.updateCartCount) {
                                window.updateCartCount(response.cartCount);
                            }
                        } else {
                            showToast('error', response.message);
                        }
                    })
                    .fail(function() {
                        showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                    })
                    .always(function() {
                        button.prop('disabled', false);
                        button.html('<i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ');
                    });
            });
        });

        function showToast(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 100px; right: 20px; z-index: 9999;">' +
                '<i class="fas ' + icon + ' me-2"></i>' + message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>');
            
            $('body').append(toast);
            
            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }
    </script>
}
