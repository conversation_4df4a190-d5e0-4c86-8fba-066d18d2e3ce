{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["gZTWnnR52OJ6oV3q1l5VL8Yi/Bu7luILxr4Oc8N8CAk=", "zJFiebf3eqKbejQrjCG05/AcD89BmLQ25uPuPmexM5k=", "Xn5CfTPE0/gLZhUWHcw1vzkbFMcc4/9Hk203TR9MeYY=", "GLH4XpXLdU4hYi7VfM2R3OgAEEm4BeWNUP1qZLuSPvQ=", "MGw45O0GH78wdLoL16VAFcnwXN+RD4URzU0QqUAfw+4=", "8ieT5um6FTLoTifigygJosySTfcJUXmmULSUbo8kWgE=", "gx00mqVFwx6Cm1vG9E++NCJpqgHDZ7NBwRjaXSWn+rE=", "KAzYnYZA9i5K6Cz48dfNPwVnbAaLXnAeJ2hO02PQz/o=", "LKIEzo+u+X27CsmMY9cd7f4BVWguGNch9snI3dYvZuw=", "yyHHCZlOR0W05M78Wz1VXn3gM2FP0jtS09ZRfsx3I1k=", "466khcagdAceqRf0qRe5EWuTrMYB/PvN4LGZe1OjNSk=", "7VcQ6a8nJUql34GZIxXaBG96fC/qmyY6BNAQatEUHf8=", "Hjwacun/F8XwAJhD9TgbvusyEGupZOA2XJsID58oh6Y=", "mHTnHIryS+3pxW/eA5QUP1FV8xjSZH1f8n+p6wgzIFs=", "fabpkXZfauZoz65m3CGpgCi9muk9RbEwONTmLg0GliA=", "q4WN4yxr329vtZdVVIxnHve7RWuVihWv7wHOrQFv3tI=", "P0fPMZHe3+7SMzooL5GEYS1wTpj6sINUvnQYdNNVN2w=", "mZX7n0bYNwLuCx76bpLGUduIAciF/3tHVnUaHoJx8mU=", "6S+TMdhH8dLgyDJNGca3Mi076bmpcxaegQlfo4po9sQ=", "lbF7hYs1T9T3KEPlcka/KsIcrJaF/1eWi8F5HhP36aM=", "0RLsJKC7p60KIHF8F6ec5g88lgKSczAtc4EQTK5O87U=", "CSsX7RXVFJd/PIg5FEJFtl6hwwYc/o8R4P4dCXn2+Yk=", "oCbxhKCASn5Hx53SyZtl1eCCnlS6OYwqoXTOtEAdaFU=", "nzT+fLk5zSfEX9Aa2PUy+4X16hgd3XGz1i4JF0jF9yE=", "hU8+nFC4nSsH1tzvXEG9/Y6cvuURr71EJWebYROAGEU=", "Jmm88K3+ToZlKS1GMGRmw6mRi5QIyL+kUV3ZC2r1uvg=", "WcpMJEFu9vNhZSUkRoM72gj/Ts0szwhi9sJVy3Px5X8=", "DTWfuThjIQGpYq2K2ITdJa8VaWN5hvf+H2TQxGv+yRY=", "Cj0hBJso+d4SJE+BBqkMDejcArqPpQPBo4umT6oXV1E=", "nONV/9zCesIR4vSsR37ilcCKwUAK58O9rzZGjtjMACQ=", "2ZfeUKW/7V73eDwWSScBr/P7UkqIZbBN5UUiLlUFGug=", "qnbZ4YyAyjThz+Ly1PsXGh3T1hEMw1fKFmDvP4ipYWQ=", "eL6PHXbKNWY0mAgnpQXE+FPpsjanTzHDYh/2CBtPiIs=", "p6aAzo12MovrA74WofHcXrl79TZeRcXMDI68UV6jQPk=", "g2MFVUze+r5zNpdk2mYPZxtGjuAH8TvXGt6W6Ewr474=", "rx/DfOmTabak0OwiAhxJ33iddpWqLhcs9/zrvJk7jlw=", "HPdhDeZLWSOWt/gaevHrLvqqhtrv7wxOZ9wPCrqZUuI=", "mSk4SAkJdfZ1R6V7tdDX8lcoCC5z8BD9kVhz9RHNv28=", "W38jIi50jGHYCGJn8hb7W53aEC6NxTdU3Hna40mXg7U=", "/dovdSr9JsvBD9LkSScjEXBzUtbAzkSXRWtOJa8kjZg=", "Stx5Ud67Vc7P82vdyNeNR1+PXSHOKc0+PYAFt5umHkQ=", "As2mAl3N1g7oS+Pp4IN6TTvMiQIHAnGYVOM3NI5Z5ww=", "FCKJooQgjCnseWFL3DK13TwExIEN2bc6pVVcBqpp2io=", "gYJQun6ORDbLe9Kl5LTYb9n6tiKB/X6in1XCIp1R4lQ=", "KZXnzzTwkmLKa7FPkWSrf53Kix85JBQwwqq/6Hqn2Ok=", "IWh3LZHtThhxVJqLDsKZ+mliNjEQqwk1w3+p8ZtQwkA=", "0/WQjWHYP0hHA7qej5m+qcEfQ/c1Oi/aQPl9guRzOq4=", "wxo7mo2dazJlQTrPJE8k2iXpj8r5BzKhjcqIMMDBaGQ=", "tPKprulTn0/w1aL1hXYvSXV8QyI+7LQ6Z6mpquXTTjk=", "f3/xCwnCPyeFjRQV6fH8OnNWq1pUEi8GyvT8MI8PJm8=", "Db5VgGoBz5p14TFDdCYj6EhCkpgAmoSJHtpcUINEPUw=", "5pOqrns44fi4Eti508iLlm4g9tL8Bq1/DMZrkFR3so4=", "v+rwiK34lV6920TrKOx2JbSZmnLpV+DBqTWM+T2zlG8=", "kxr75ZQRy2//2AdQHgD+i9e8W/5KWFF1BRro7dn5qhM=", "EwrnUMrsdXkDA4XITKL5JvDX8ctRWwDS5v3dWYOj8Cc=", "AloPH/qYMlOuyNrDbU9PpCq+kMjtgveyCJrmi1a0KHY=", "RFXiqZ+CH3jmMpneLc5ypYbXp7gSi1YZ5/AvorbUqrc=", "5UZnt3TyZPyw2eujJ2Hb+E5XuP+/7nxPtfxbN7qj73o=", "85D3lBIlALTHeWhUMEUbLwBPiPYXIw4uqazQH2b8Oy0=", "wg9Y1Dla5Fk1oN+yAe9LpTXn/8xSEBWodPSN386Tseg=", "AWD3g9gdRwp9BXQlwOz7jGTl0z48F1MGh9NFOt8ciVQ=", "ROX5uartYXzKXnTK9O3MqUrT/QD0h+hzLA2mwX0lhvQ=", "x4iZFsVugQNhScN57BFOzWiqQ9nWMGktGnLwWztBiAk=", "OKnnfpV1jI7hCncHWn7ICc33e1wm7QyxGQV2yb3031E=", "g1wzEkSAPwDEGMC3y0JoN6N833yegC51bbbBnQKfPa0=", "lIx8ZiJjGEaHJ7szNJAoX3D1hgdS4c29NM3AI0rg414=", "us3oPJfQuJQ5FfYg1zgUyjCX24rRsGr/HXmZDGELFG8=", "NHB5rYmsngTE+Peorlh3CA0KdzmYKI4zmj9MiSUkfWI=", "hFnh8TlDXXIJCHfBAzern2emDn3OnKa5cEp/ySb6+FQ=", "7GQ3SV0kFM9SY8/k2lj9jPFa6XpJ3vvfX4R/SEXR5oE=", "FDXD5qf+DNIOQdO/tS7DLTs7rM8ywhZdAsOAFQuxW9Y=", "7vQSfOIPMcTAXe/y08ihppmzkp4tPBmdYXhy9m7hdVs=", "HPvmD9q5cCd6aCzUmVT27sZ1yorjAEHfcMmB7+bx0Ro=", "JgrtHXiojGEufJql09ZNBPMTtLqCLDHzBRt0B21cXxg=", "ohZTPB5dX4TzL6ypUaO05n2R1VDXLwQ+N242NNpnK74=", "hobUpAc7p+baC7RKmxVhPjLoZDo1uNopIBdNztaR8VY=", "DVRZnQf+ugXza/wAxOhtm47maAtZIIU4f8pdxokcuqk=", "kFLxlv1evhHtz5JzQMZ8F4S14opoAMAF3BrdcT3xNqg=", "3RO1j0NWGfhHYXDL0aDxsCN50wqv8EEtk0f8ubuI728=", "8FbVjJPnIAzLm81XKj7cILURSPZHkkpRpKECEna6Fhs=", "cg1KH/W88ko/dPFElukaLOpY5/i50Ks4Vo22Or0xmFY=", "Q3leZdYpD3qUeDb4fgfSsj0pDehmyLsTJWyZlzpwmIY=", "8Gb7PmrqPRjsig0HfC7TtanKrO3p41CEkl+S+tTAeDU=", "h86Fhp7aoChl1z5EgwzZVI2VQsbE/kGykma0PmjkSsU=", "frqFZ2FlXr8snHL7X8m2G3DiAlm3bTKmsMRMc9NAVug=", "7y802IDsNcKQpU9qZsTF6pWmj0qiiUuavHvnxc3xQEs=", "sFrUDImFvspkwhvUSnrU4qpjtzKm4+NJuIk8shnGRbE=", "v5ZIuoJV6ajYxh8WpQsetfAW8jzRE2pY9p2tD/Ptzdg=", "+9WZD6gzL3wRUxh5i6RdPx18EUFFdHk9Dcf4RHsW5Y8=", "6n02y+0nqo5RN3ay0sieE3nZUjWIpNePWEPYb8AJFYg=", "3/NxIwFa24kX5M8D+8mHTlE0iD20lfyyXjnG7hIkxB0=", "h3EVi3V76QRa4DR9GVttuDLyF3FsFbOeAe4JufJaPGM=", "j0IPGNlBqIursa4GqgwNeZu6rDrvHGJc6oW/fJ3tpiM=", "8d/NGObZxMLGWPRlfpIkjkG+PP3Da8tRZLANdPR56Gs=", "nUSB8XU61Ea2jOdKU7vTPZv2C+u/zdF/YUVZ4anXQp8=", "NbsbTf5cUh4vXVcegyQzyJ6BNYIAKiDRdRMHEiVGEe8=", "6eqlEgWQKpqy0zR0quOBi/59sq1oFbWeaFWdpKHE5yg=", "JUwVKqk3qIwE9hHy4iXgZdDvh1+fTTIfFxRIJ0JsnHE=", "GXnWjoriC3Xk6clpPu1ndIGSPjimhihox/v0DcdwgcQ=", "Omc8VbtN9FlvhdXh4lVo/Mqz6uXg/n8z9NRBS2qOysE=", "X7OwM/NG/FaSIQSXudPWBIH8y+rAOxV26FmOHGNU2cc=", "b8UJYr8U9pcoLNHkvNrzcn89VipCruPesEYEj8PvzDE=", "o/5J9N3TlDdEzzPfvw2++Cwp0aDv/RGX4cRnpB09L3U=", "aZ2/XAZouKagTTRszu1BjLb4OCNn2BrXs0NrC2H3Fk8=", "Mlc0lL2pW2YMlm+Uiptggsfqcu8PlnsrWEDMGx4yHLE=", "VTGy6bNw6Y+WxKopJFfoojNVHJfBneQB3EeMd3mEF5I=", "mU5bLl28jeD4OyZbdgLzrtqxtgUS7nRBAufadUQ5lAo=", "/VcV/rFHP5G5MZMat8bB46mUbXJN1m95OxNAxGJErgY=", "NJQmE+d1GFsZHCntABA6O/FuaNgmuwpfKkSLz5xH0x8=", "+p4mFoh9+wVdQhm+v0doa3JGyL3GNSjUOGW6BEIm3wE=", "L9gSphwhxu9lpl0oTUlxh9WErAYMqUge/sQITnUONGo=", "uyc0vjhcyJa5FsObx7VkHGusp4mcG/WVPhcgpIJMlvM=", "NtR1FZS3u3wx/gZHIujuCOEnAOS0AZlM6dViiEFEcGE=", "xzbJTgCHwfSUN/0Jy1os7kdmNMN+OOWYtnmCklSZc38=", "+BrepoMRwbeCMNsiad70zEVLZhBzMtnly5VJLwSYnhw=", "ZgFFgb5YpHC3VYLuziq6XH5ccIS6uD9unGqDb1PtCQ8=", "hj8U0kjqBAaYCztH8aeU9ltIpb4JN/ITX/q0eurzK9M=", "xazC+jF4eDNvk4w1XhJA7hAGLw92u2dxXkndua6Skk4=", "CCGuJh6XjzEVHc7dPL2g1MgLzYyx/v1y+LijO2dWvAs=", "PA4reHtMOMSAqIb3SXPDfLPtRBNInHhA/6927D2dYKY=", "lG4+LuOZX9O8a6VztPvTv12vjn+kAuYx/iBt1cpkJww=", "Q+DbZaPmZ0TMh4aSIjksA5hr4errFwV23FWp82GyanA=", "wXfw60KZGGp9A47sl05AjY3zJH0qWaXjCQNAxfE0m4U=", "ObiKEsD5ZQAYx8bXTNkdcCwZnVpAmHmCwfa5iIVrMf8=", "/NBaklxYEQaGKpgACDOgb4gVxOt0wIcirYD85xVHWMQ=", "c3qFO3mJ6HvyD0bSnZAqUesLvyCPHlDq2CTUQ89u3/c=", "0vrypP5ULuClvlrP9jOMbNFCv29w7ndw6+LLF2c8BVE=", "zl8nL/emzKtq/4puyseeJ7yqX0nGOhkU24Rm90EXJp8=", "l4/Epj4v4H/cg7I1iI8WGxMTL0S6Jcnv2jbT9Qvv4ng=", "q9tyBRLdSFZjA359lEU7h2WWzp380yXhL5qVb6jGefg=", "+Z7uYP0PgOeAVEtv9qOl2+Pb89KqdzqWCjnmS6etohE=", "KNWhiaL9Nf+UiA+y5B5ZyqCmGPz3926pxogHP8hPT7o=", "uVceJztRZva36KAF12mqdQo8jUQ620PWT6sYjBdgmuc=", "SyvGCo9SbjbB6coehAekKgKQISo8hC8Y0D4F1fy8ArU=", "2CnYDHS/ptdz0wJMZAf42396dRWQ0bLbEaUWjo344JY="], "CachedAssets": {"gZTWnnR52OJ6oV3q1l5VL8Yi/Bu7luILxr4Oc8N8CAk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-22T15:56:13.9117456+00:00"}, "zJFiebf3eqKbejQrjCG05/AcD89BmLQ25uPuPmexM5k=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-22T15:56:13.913274+00:00"}, "Xn5CfTPE0/gLZhUWHcw1vzkbFMcc4/9Hk203TR9MeYY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "GLH4XpXLdU4hYi7VfM2R3OgAEEm4BeWNUP1qZLuSPvQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "MGw45O0GH78wdLoL16VAFcnwXN+RD4URzU0QqUAfw+4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "8ieT5um6FTLoTifigygJosySTfcJUXmmULSUbo8kWgE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "gx00mqVFwx6Cm1vG9E++NCJpqgHDZ7NBwRjaXSWn+rE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "KAzYnYZA9i5K6Cz48dfNPwVnbAaLXnAeJ2hO02PQz/o=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "LKIEzo+u+X27CsmMY9cd7f4BVWguGNch9snI3dYvZuw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "yyHHCZlOR0W05M78Wz1VXn3gM2FP0jtS09ZRfsx3I1k=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-22T15:56:14.0542706+00:00"}, "466khcagdAceqRf0qRe5EWuTrMYB/PvN4LGZe1OjNSk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "7VcQ6a8nJUql34GZIxXaBG96fC/qmyY6BNAQatEUHf8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "Hjwacun/F8XwAJhD9TgbvusyEGupZOA2XJsID58oh6Y=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "mHTnHIryS+3pxW/eA5QUP1FV8xjSZH1f8n+p6wgzIFs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "fabpkXZfauZoz65m3CGpgCi9muk9RbEwONTmLg0GliA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "q4WN4yxr329vtZdVVIxnHve7RWuVihWv7wHOrQFv3tI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "P0fPMZHe3+7SMzooL5GEYS1wTpj6sINUvnQYdNNVN2w=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "mZX7n0bYNwLuCx76bpLGUduIAciF/3tHVnUaHoJx8mU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "6S+TMdhH8dLgyDJNGca3Mi076bmpcxaegQlfo4po9sQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "lbF7hYs1T9T3KEPlcka/KsIcrJaF/1eWi8F5HhP36aM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-22T15:56:13.9841908+00:00"}, "0RLsJKC7p60KIHF8F6ec5g88lgKSczAtc4EQTK5O87U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "CSsX7RXVFJd/PIg5FEJFtl6hwwYc/o8R4P4dCXn2+Yk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "oCbxhKCASn5Hx53SyZtl1eCCnlS6OYwqoXTOtEAdaFU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "nzT+fLk5zSfEX9Aa2PUy+4X16hgd3XGz1i4JF0jF9yE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "hU8+nFC4nSsH1tzvXEG9/Y6cvuURr71EJWebYROAGEU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "Jmm88K3+ToZlKS1GMGRmw6mRi5QIyL+kUV3ZC2r1uvg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "WcpMJEFu9vNhZSUkRoM72gj/Ts0szwhi9sJVy3Px5X8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "DTWfuThjIQGpYq2K2ITdJa8VaWN5hvf+H2TQxGv+yRY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "Cj0hBJso+d4SJE+BBqkMDejcArqPpQPBo4umT6oXV1E=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-22T15:56:14.0171599+00:00"}, "nONV/9zCesIR4vSsR37ilcCKwUAK58O9rzZGjtjMACQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-22T15:56:14.0507615+00:00"}, "2ZfeUKW/7V73eDwWSScBr/P7UkqIZbBN5UUiLlUFGug=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "qnbZ4YyAyjThz+Ly1PsXGh3T1hEMw1fKFmDvP4ipYWQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "eL6PHXbKNWY0mAgnpQXE+FPpsjanTzHDYh/2CBtPiIs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "p6aAzo12MovrA74WofHcXrl79TZeRcXMDI68UV6jQPk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "g2MFVUze+r5zNpdk2mYPZxtGjuAH8TvXGt6W6Ewr474=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-22T15:56:13.9841908+00:00"}, "rx/DfOmTabak0OwiAhxJ33iddpWqLhcs9/zrvJk7jlw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "HPdhDeZLWSOWt/gaevHrLvqqhtrv7wxOZ9wPCrqZUuI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-22T15:56:14.0542706+00:00"}, "mSk4SAkJdfZ1R6V7tdDX8lcoCC5z8BD9kVhz9RHNv28=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "W38jIi50jGHYCGJn8hb7W53aEC6NxTdU3Hna40mXg7U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "/dovdSr9JsvBD9LkSScjEXBzUtbAzkSXRWtOJa8kjZg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-22T15:56:13.9990274+00:00"}, "Stx5Ud67Vc7P82vdyNeNR1+PXSHOKc0+PYAFt5umHkQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-22T15:56:13.9327034+00:00"}, "As2mAl3N1g7oS+Pp4IN6TTvMiQIHAnGYVOM3NI5Z5ww=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "FCKJooQgjCnseWFL3DK13TwExIEN2bc6pVVcBqpp2io=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "gYJQun6ORDbLe9Kl5LTYb9n6tiKB/X6in1XCIp1R4lQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "KZXnzzTwkmLKa7FPkWSrf53Kix85JBQwwqq/6Hqn2Ok=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "IWh3LZHtThhxVJqLDsKZ+mliNjEQqwk1w3+p8ZtQwkA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-22T15:56:13.983181+00:00"}, "0/WQjWHYP0hHA7qej5m+qcEfQ/c1Oi/aQPl9guRzOq4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "wxo7mo2dazJlQTrPJE8k2iXpj8r5BzKhjcqIMMDBaGQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "tPKprulTn0/w1aL1hXYvSXV8QyI+7LQ6Z6mpquXTTjk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "f3/xCwnCPyeFjRQV6fH8OnNWq1pUEi8GyvT8MI8PJm8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "Db5VgGoBz5p14TFDdCYj6EhCkpgAmoSJHtpcUINEPUw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-22T15:56:13.913274+00:00"}, "5pOqrns44fi4Eti508iLlm4g9tL8Bq1/DMZrkFR3so4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "v+rwiK34lV6920TrKOx2JbSZmnLpV+DBqTWM+T2zlG8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "kxr75ZQRy2//2AdQHgD+i9e8W/5KWFF1BRro7dn5qhM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "EwrnUMrsdXkDA4XITKL5JvDX8ctRWwDS5v3dWYOj8Cc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "AloPH/qYMlOuyNrDbU9PpCq+kMjtgveyCJrmi1a0KHY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "RFXiqZ+CH3jmMpneLc5ypYbXp7gSi1YZ5/AvorbUqrc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-22T15:56:14.0070253+00:00"}, "5UZnt3TyZPyw2eujJ2Hb+E5XuP+/7nxPtfxbN7qj73o=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "85D3lBIlALTHeWhUMEUbLwBPiPYXIw4uqazQH2b8Oy0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-22T15:56:13.998029+00:00"}, "wg9Y1Dla5Fk1oN+yAe9LpTXn/8xSEBWodPSN386Tseg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-22T15:56:14.0000282+00:00"}, "AWD3g9gdRwp9BXQlwOz7jGTl0z48F1MGh9NFOt8ciVQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-22T15:56:13.9174448+00:00"}, "ROX5uartYXzKXnTK9O3MqUrT/QD0h+hzLA2mwX0lhvQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-22T15:56:13.9327034+00:00"}, "uVceJztRZva36KAF12mqdQo8jUQ620PWT6sYjBdgmuc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\16x5hptwnm-wxukb43dng.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "css/site#[.{fingerprint=wxukb43dng}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hp601njcaa", "Integrity": "9a65+SejG8UWaTBBAjcCAVXmXZNFBgkZThPY3PS3LkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\css\\site.css", "FileLength": 974, "LastWriteTime": "2025-07-22T16:46:11.7997927+00:00"}, "x4iZFsVugQNhScN57BFOzWiqQ9nWMGktGnLwWztBiAk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\f2n19trgho-61n19gt1b8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "HPvmD9q5cCd6aCzUmVT27sZ1yorjAEHfcMmB7+bx0Ro=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4bguza2wwq-xtxxf3hu2r.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "JgrtHXiojGEufJql09ZNBPMTtLqCLDHzBRt0B21cXxg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gvreqwmawe-bqjiyaj88i.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "ohZTPB5dX4TzL6ypUaO05n2R1VDXLwQ+N242NNpnK74=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8vcgkrwmkl-c2jlpeoesf.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-22T15:56:13.983181+00:00"}, "hobUpAc7p+baC7RKmxVhPjLoZDo1uNopIBdNztaR8VY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v69uv9l2lh-erw9l3u2r3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-22T15:56:13.9845427+00:00"}, "DVRZnQf+ugXza/wAxOhtm47maAtZIIU4f8pdxokcuqk=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\8cyuw1473v-aexeepp0ev.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "kFLxlv1evhHtz5JzQMZ8F4S14opoAMAF3BrdcT3xNqg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\x6nrwiogo5-d7shbmvgxk.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "3RO1j0NWGfhHYXDL0aDxsCN50wqv8EEtk0f8ubuI728=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\28731eqxw8-ausgxo2sd3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-22T15:56:13.913274+00:00"}, "8FbVjJPnIAzLm81XKj7cILURSPZHkkpRpKECEna6Fhs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\9mdthi67dv-k8d9w2qqmf.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "cg1KH/W88ko/dPFElukaLOpY5/i50Ks4Vo22Or0xmFY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\g6qlq9wzep-cosvhxvwiu.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-22T15:56:13.9327034+00:00"}, "Q3leZdYpD3qUeDb4fgfSsj0pDehmyLsTJWyZlzpwmIY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\l1j5xbo07o-ub07r2b239.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-22T15:56:13.9340119+00:00"}, "8Gb7PmrqPRjsig0HfC7TtanKrO3p41CEkl+S+tTAeDU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gfkyhpalvi-fvhpjtyr6v.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "h86Fhp7aoChl1z5EgwzZVI2VQsbE/kGykma0PmjkSsU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\t6p4p6n6yb-b7pk76d08c.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "frqFZ2FlXr8snHL7X8m2G3DiAlm3bTKmsMRMc9NAVug=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\v9iccpy2kn-fsbi9cje9m.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "7y802IDsNcKQpU9qZsTF6pWmj0qiiUuavHvnxc3xQEs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7oe5vyyx9w-rzd6atqjts.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "sFrUDImFvspkwhvUSnrU4qpjtzKm4+NJuIk8shnGRbE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\qh82bmls2l-ee0r1s7dh0.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "v5ZIuoJV6ajYxh8WpQsetfAW8jzRE2pY9p2tD/Ptzdg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\txactiogbi-dxx9fxp4il.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "+9WZD6gzL3wRUxh5i6RdPx18EUFFdHk9Dcf4RHsW5Y8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1zmedvy5kz-jd9uben2k1.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-22T15:56:13.9117456+00:00"}, "6n02y+0nqo5RN3ay0sieE3nZUjWIpNePWEPYb8AJFYg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\oznnqchpzv-khv3u5hwcm.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-22T15:56:13.913274+00:00"}, "3/NxIwFa24kX5M8D+8mHTlE0iD20lfyyXjnG7hIkxB0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ssnfiv1yzk-r4e9w2rdcm.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-22T15:56:13.9340119+00:00"}, "h3EVi3V76QRa4DR9GVttuDLyF3FsFbOeAe4JufJaPGM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\loxz0wgd2r-lcd1t2u6c8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "j0IPGNlBqIursa4GqgwNeZu6rDrvHGJc6oW/fJ3tpiM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7qdbtgp7hb-c2oey78nd0.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "8d/NGObZxMLGWPRlfpIkjkG+PP3Da8tRZLANdPR56Gs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\p5smgi897r-tdbxkamptv.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "nUSB8XU61Ea2jOdKU7vTPZv2C+u/zdF/YUVZ4anXQp8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3z0hydztvm-j5mq2jizvt.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "NbsbTf5cUh4vXVcegyQzyJ6BNYIAKiDRdRMHEiVGEe8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mr8ot3pktr-06098lyss8.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "6eqlEgWQKpqy0zR0quOBi/59sq1oFbWeaFWdpKHE5yg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jrzceuklaq-nvvlpmu67g.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "JUwVKqk3qIwE9hHy4iXgZdDvh1+fTTIfFxRIJ0JsnHE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\4rre1lw2rt-s35ty4nyc5.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "GXnWjoriC3Xk6clpPu1ndIGSPjimhihox/v0DcdwgcQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\remhgu4vv1-pj5nd1wqec.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "Omc8VbtN9FlvhdXh4lVo/Mqz6uXg/n8z9NRBS2qOysE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o1lnyx5mc6-46ein0sx1k.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "X7OwM/NG/FaSIQSXudPWBIH8y+rAOxV26FmOHGNU2cc=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\u7u39a79im-v0zj4ognzu.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "b8UJYr8U9pcoLNHkvNrzcn89VipCruPesEYEj8PvzDE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\p9cvd16lt0-37tfw0ft22.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "o/5J9N3TlDdEzzPfvw2++Cwp0aDv/RGX4cRnpB09L3U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mzimungl5e-hrwsygsryq.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-22T15:56:13.998029+00:00"}, "aZ2/XAZouKagTTRszu1BjLb4OCNn2BrXs0NrC2H3Fk8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7h6yj91wkq-pk9g2wxc8p.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-22T15:56:14.0010253+00:00"}, "Mlc0lL2pW2YMlm+Uiptggsfqcu8PlnsrWEDMGx4yHLE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\r756wof0qh-ft3s53vfgj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-22T15:56:14.0101216+00:00"}, "VTGy6bNw6Y+WxKopJFfoojNVHJfBneQB3EeMd3mEF5I=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\3m7f91nier-6cfz1n2cew.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-22T15:56:14.01516+00:00"}, "mU5bLl28jeD4OyZbdgLzrtqxtgUS7nRBAufadUQ5lAo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jw5a92aq3t-6pdc2jztkx.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "/VcV/rFHP5G5MZMat8bB46mUbXJN1m95OxNAxGJErgY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\gyuwpau4zj-493y06b0oq.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "NJQmE+d1GFsZHCntABA6O/FuaNgmuwpfKkSLz5xH0x8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pb4x9icssc-iovd86k7lj.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "+p4mFoh9+wVdQhm+v0doa3JGyL3GNSjUOGW6BEIm3wE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\wru9w095ou-vr1egmr9el.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "L9gSphwhxu9lpl0oTUlxh9WErAYMqUge/sQITnUONGo=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\sao04fkaau-kbrnm935zg.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-22T15:56:13.9350262+00:00"}, "uyc0vjhcyJa5FsObx7VkHGusp4mcG/WVPhcgpIJMlvM=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vzi8lrz6wj-jj8uyg4cgr.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "NtR1FZS3u3wx/gZHIujuCOEnAOS0AZlM6dViiEFEcGE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xxbtc3h5e2-y7v9cxd14o.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "xzbJTgCHwfSUN/0Jy1os7kdmNMN+OOWYtnmCklSZc38=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\thvypom8t2-notf2xhcfb.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "+BrepoMRwbeCMNsiad70zEVLZhBzMtnly5VJLwSYnhw=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\471uhqs47y-h1s4sie4z3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-22T15:56:14.0020254+00:00"}, "ZgFFgb5YpHC3VYLuziq6XH5ccIS6uD9unGqDb1PtCQ8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\g52nsnploq-63fj8s7r0e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-22T15:56:14.0121591+00:00"}, "hj8U0kjqBAaYCztH8aeU9ltIpb4JN/ITX/q0eurzK9M=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\jj2z1lxy1f-0j3bgjxly4.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-22T15:56:14.0171599+00:00"}, "xazC+jF4eDNvk4w1XhJA7hAGLw92u2dxXkndua6Skk4=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\b0wdyvoq0e-47otxtyo56.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-22T15:56:14.0389852+00:00"}, "CCGuJh6XjzEVHc7dPL2g1MgLzYyx/v1y+LijO2dWvAs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\mgsn4a7rqb-4v8eqarkd7.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-22T15:56:13.9117456+00:00"}, "PA4reHtMOMSAqIb3SXPDfLPtRBNInHhA/6927D2dYKY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o6xdbukn84-356vix0kms.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-22T15:56:13.9117456+00:00"}, "lG4+LuOZX9O8a6VztPvTv12vjn+kAuYx/iBt1cpkJww=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\za7ti5z8ew-83jwlth58m.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-22T15:56:13.913274+00:00"}, "Q+DbZaPmZ0TMh4aSIjksA5hr4errFwV23FWp82GyanA=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\pimg4bcchp-mrlpezrjn3.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "wXfw60KZGGp9A47sl05AjY3zJH0qWaXjCQNAxfE0m4U=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\dana2szpqu-lzl9nlhx6b.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "ObiKEsD5ZQAYx8bXTNkdcCwZnVpAmHmCwfa5iIVrMf8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\okmrmed9t4-ag7o75518u.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "/NBaklxYEQaGKpgACDOgb4gVxOt0wIcirYD85xVHWMQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\52kpdtld7s-x0q3zqp4vz.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-22T15:56:13.9662933+00:00"}, "c3qFO3mJ6HvyD0bSnZAqUesLvyCPHlDq2CTUQ89u3/c=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\bnuj1lyqi8-0i3buxo5is.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-22T15:56:13.983181+00:00"}, "0vrypP5ULuClvlrP9jOMbNFCv29w7ndw6+LLF2c8BVE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\acz1cnk0ic-o1o13a6vjx.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "zl8nL/emzKtq/4puyseeJ7yqX0nGOhkU24Rm90EXJp8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\y7pt8v4sl0-ttgo8qnofa.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-22T15:56:14.0010253+00:00"}, "l4/Epj4v4H/cg7I1iI8WGxMTL0S6Jcnv2jbT9Qvv4ng=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\wxr78b97uf-2z0ns9nrw6.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-22T15:56:13.9189526+00:00"}, "q9tyBRLdSFZjA359lEU7h2WWzp380yXhL5qVb6jGefg=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\1bbela6ocw-muycvpuwrr.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-22T15:56:13.9327034+00:00"}, "+Z7uYP0PgOeAVEtv9qOl2+Pb89KqdzqWCjnmS6etohE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\vhe5vki0ai-87fc7y1x7t.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-22T15:56:13.950672+00:00"}, "KNWhiaL9Nf+UiA+y5B5ZyqCmGPz3926pxogHP8hPT7o=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\m99m346zva-mlv21k5csn.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-22T15:56:13.9845427+00:00"}, "SyvGCo9SbjbB6coehAekKgKQISo8hC8Y0D4F1fy8ArU=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\668bouo4dg-aw1qx0tx3t.gz", "SourceId": "GocPho", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "GocPho#[.{fingerprint=aw1qx0tx3t}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GocPho.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sfm6ylns6", "Integrity": "nWi02AOeQbOmvfr1vnP/PEVT3GwKm/0oJDOvIaablPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GocPho.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "2CnYDHS/ptdz0wJMZAf42396dRWQ0bLbEaUWjo344JY=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ajdd70ldzy-aw1qx0tx3t.gz", "SourceId": "GocPho", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "GocPho#[.{fingerprint=aw1qx0tx3t}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\GocPho.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sfm6ylns6", "Integrity": "nWi02AOeQbOmvfr1vnP/PEVT3GwKm/0oJDOvIaablPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\GocPho.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-22T15:56:13.9854063+00:00"}, "NHB5rYmsngTE+Peorlh3CA0KdzmYKI4zmj9MiSUkfWI=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\o900pz65p5-76dwl5a5pz.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-cup#[.{fingerprint=76dwl5a5pz}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-cup.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "06dd28rakr", "Integrity": "6BVHiZz+z5YgCxGSTn8bfSlaktROMvapCFGUzA9OSao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-cup.svg", "FileLength": 356, "LastWriteTime": "2025-07-22T16:17:18.9668764+00:00"}, "hFnh8TlDXXIJCHfBAzern2emDn3OnKa5cEp/ySb6+FQ=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\xhkei0fxtn-40h8kgmqbi.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-hero#[.{fingerprint=40h8kgmqbi}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-hero.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "occz3ru07s", "Integrity": "SDhge9l9/LsPuJl7+zUbNWmRzxCeVk0pRklyKCVjsAA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-hero.svg", "FileLength": 361, "LastWriteTime": "2025-07-22T16:17:18.9668764+00:00"}, "FDXD5qf+DNIOQdO/tS7DLTs7rM8ywhZdAsOAFQuxW9Y=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\f8fht7pf4z-ybrhg4el0e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/default-coffee#[.{fingerprint=ybrhg4el0e}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\default-coffee.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2m3i9wiy3f", "Integrity": "NlQVYx+EHBaM982sSSbDAzbxMJI56MLQzsgzRyV4ZI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\default-coffee.svg", "FileLength": 349, "LastWriteTime": "2025-07-22T16:17:18.9760977+00:00"}, "7vQSfOIPMcTAXe/y08ihppmzkp4tPBmdYXhy9m7hdVs=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\fcrk7cv3ph-1n5x6omr1j.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/placeholder-generator#[.{fingerprint=1n5x6omr1j}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\placeholder-generator.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mikg0yjy4", "Integrity": "QIuJ+vtMxApAT2gQtDS+Cq1Ba95iXhVG+fhRf3Orhj8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\placeholder-generator.html", "FileLength": 664, "LastWriteTime": "2025-07-22T16:17:18.9668764+00:00"}, "OKnnfpV1jI7hCncHWn7ICc33e1wm7QyxGQV2yb3031E=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\fuu1oae2tc-gobymrk17e.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/bac-xiu#[.{fingerprint=gobymrk17e}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\bac-xiu.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q2mudwsy9c", "Integrity": "QrhqkB+GPsx2hYs8TCseBVtxmmdsqpXe4ZbQEy3LDYg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\bac-xiu.svg", "FileLength": 617, "LastWriteTime": "2025-07-22T16:40:42.5702021+00:00"}, "g1wzEkSAPwDEGMC3y0JoN6N833yegC51bbbBnQKfPa0=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\6c0sv7su9y-gllmg658ug.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/ca-phe-den-da#[.{fingerprint=gllmg658ug}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-den-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7fldkgrbw", "Integrity": "Of3tWH/Gp/Qx/p0+Jo28sfGIkSIppiIhByEEjQSXBxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-den-da.svg", "FileLength": 543, "LastWriteTime": "2025-07-22T16:40:42.5702021+00:00"}, "lIx8ZiJjGEaHJ7szNJAoX3D1hgdS4c29NM3AI0rg414=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\7uud018py2-ufaql7fegv.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/ca-phe-sua-da#[.{fingerprint=ufaql7fegv}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-sua-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x9txfpaaag", "Integrity": "daWx9VTwRml1RtxQdq9o6V451Dd/GbYCDegCxgBTLU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\ca-phe-sua-da.svg", "FileLength": 623, "LastWriteTime": "2025-07-22T16:40:42.5712429+00:00"}, "us3oPJfQuJQ5FfYg1zgUyjCX24rRsGr/HXmZDGELFG8=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\ohr6whz1i7-k4qxzznt7v.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-beans#[.{fingerprint=k4qxzznt7v}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-beans.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2fzo05wfz7", "Integrity": "skLCbTAzp76+sK5pUUlyn5loE3fbxjV+gKlXMRGnLZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-beans.svg", "FileLength": 555, "LastWriteTime": "2025-07-22T16:40:42.5722453+00:00"}, "7GQ3SV0kFM9SY8/k2lj9jPFa6XpJ3vvfX4R/SEXR5oE=": {"Identity": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\hgx7ek3gn4-ih0w9tino9.gz", "SourceId": "GocPho", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\GocPho\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GocPho", "RelativePath": "images/coffee-shop-interior#[.{fingerprint=ih0w9tino9}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-shop-interior.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "guxsjs6w8i", "Integrity": "qCJ4ofKth/ufGLPNL3O3k6wqKeDnXWi4+WiUMBdBImQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\GocPho\\wwwroot\\images\\coffee-shop-interior.svg", "FileLength": 516, "LastWriteTime": "2025-07-22T16:40:42.5702021+00:00"}}, "CachedCopyCandidates": {}}